<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Traits\LastLogInLocationTrait;
use App\Traits\Passport\PassportRedirectTrait;
use App\Traits\UserAlreadyLoginTrait;
use App\Traits\UserLogoutTrait;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;
    use PassportRedirectTrait;
    use UserLogoutTrait;
    use UserAlreadyLoginTrait;
    use LastLogInLocationTrait;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }

    /**
     * Show the application's login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        Log::info('showLoginForm called with parameters: ' . json_encode(request()->all()));

        // Only set redirect URI if we have OAuth parameters or this is coming from OAuth flow
        // Temporarily disabled to debug redirect loop
        // if ($this->shouldSetRedirectUri()) {
        //     $this->setRedirectUri();
        // }

        // Handle registration flow and get appropriate response
        // This needs to be processed regardless of redirect URI setting
        $response = $this->processRegistrationFlow();
        if ($response) {
            Log::info('processRegistrationFlow returned a response, redirecting');
            return $response;
        }

        Log::info('Showing login view');
        return view('auth.login');
    }

    /**
     * Check if we should set redirect URI based on request context.
     *
     * @return bool
     */
    private function shouldSetRedirectUri(): bool
    {
        // Set redirect URI if we have OAuth parameters
        if (request()->has(['client_id', 'redirect_uri', 'response_type'])) {
            return true;
        }

        // Set redirect URI if we have registration parameters
        if (request()->has('is_register')) {
            return true;
        }

        // Set redirect URI if we have intended URL in session
        if (session()->has('url.intended')) {
            return true;
        }

        // Don't set redirect URI for normal login page access
        return false;
    }

    /**
     * Process registration flow and return appropriate response.
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\View\View|null
     */
    private function processRegistrationFlow()
    {
        // Check for direct registration parameters in request
        if (request()->has('is_register') && request()->get('is_register') == '1') {
            Log::info('Processing direct registration flow from request parameters');

            $email = request()->get('email');

            // Check if email exists
            if ($email && $this->emailExists($email)) {
                Log::info('Email exists, showing login form with pre-filled email');
                return view('auth.login', ['prefill_email' => $email]);
            }

            // Check if it's a Gmail address for Google OAuth
            if ($email && $this->isGmail($email)) {
                Log::info('Gmail address detected, redirecting to Google OAuth');
                return to_route('passport.socialite.redirect', 'google');
            }

            // Redirect to registration with email parameter
            Log::info('New email or no email, redirecting to registration');
            $params = $email ? ['email' => $email] : [];
            return to_route('register', $params);
        }

        // Only process session-based registration flow if we have explicit registration parameters
        // This prevents normal login access from triggering registration flow
        if (!$this->hasRegistrationParameters()) {
            return null;
        }

        // Handle new registration flow logic
        $registrationFlow = $this->handleRegistrationFlow();
        if ($registrationFlow !== false) {
            return $this->handleRegistrationFlowResponse($registrationFlow);
        }

        // Fallback to legacy registration detection for backward compatibility
        $params = $this->isRegister();
        if ($params !== false) {
            return $this->handleLegacyRegistrationResponse($params);
        }

        return null;
    }

    /**
     * Check if the current request has registration parameters.
     *
     * @return bool
     */
    private function hasRegistrationParameters(): bool
    {
        // Check if current request has is_register parameter
        if (request()->has('is_register') && request()->get('is_register') == '1') {
            Log::info('Found is_register parameter in request');
            return true;
        }

        // Check if session has redirect_uri with registration parameters and is_first_time flag
        if (session()->has('redirect_uri') && session()->has('is_first_time')) {
            $redirectUrl = session()->get('redirect_uri');
            $parsedUrl = parse_url((string) $redirectUrl);

            $params = [];
            parse_str(@$parsedUrl['query'], $params);

            if (isset($params['is_register']) && $params['is_register'] == '1') {
                Log::info('Found is_register parameter in session redirect_uri');
                return true;
            }
        }

        Log::info('No registration parameters found');
        return false;
    }

    /**
     * Handle response for new registration flow.
     *
     * @param array $registrationFlow
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    private function handleRegistrationFlowResponse(array $registrationFlow)
    {
        if ($registrationFlow['action'] === 'register') {
            // Check if it's a Gmail address for Google OAuth
            if ($registrationFlow['email'] && $this->isGmail($registrationFlow['email'])) {
                return to_route('passport.socialite.redirect', 'google');
            }

            // Redirect to registration with email parameter
            $params = $registrationFlow['email'] ? ['email' => $registrationFlow['email']] : [];
            return to_route('register', $params);
        }

        // Email exists, show login form with pre-filled email
        return view('auth.login', ['prefill_email' => $registrationFlow['email']]);
    }

    /**
     * Handle response for legacy registration detection.
     *
     * @param array $params
     * @return \Illuminate\Http\RedirectResponse
     */
    private function handleLegacyRegistrationResponse(array $params)
    {
        if ($this->isGmail($params['email'] ?? '')) {
            return to_route('passport.socialite.redirect', 'google');
        }

        return to_route('register', $params);
    }

    /**
     * Log the user out of the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $userId = session()->get('verify_user_id');

        $this->guard()->logout();

        Session::put('verify_user_id', $userId);

        if ($response = $this->loggedOut($request)) {
            return $response;
        }

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect('/');
    }

    /**
     * Force login a user.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function forceLogin(Request $request)
    {
        $user = User::where('email', $request->email)->first();

        if ( ! $user) {
            return redirect()->route('login')->withErrors(['email' => 'User not found']);
        }

        Auth::login($user, true);
        $this->updateLastLogInLocation();

        if ($user->verified != 1) {
            return $request->wantsJson()
                ? new JsonResponse(['message' => 'Your email address is not verified.'], 409)
                : redirect()->route('verification.notice');
        }

        $this->revokeGWToken();

        // Check if we have an authorization request in the session
        if ($request->session()->has('authRequest')) {
            $authRequest = $request->session()->pull('authRequest');

            // Build the authorization URL
            $query = http_build_query($authRequest);
            return redirect('/oauth/authorize?' . $query);
        }

        // Check if we have an authorization request in the session
        if (session()->has('redirect_uri')) {
            return redirect(Session::get('redirect_uri'));
        }

        return redirect()->route('login');
    }

    /**
     * Send the response after the user was authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    protected function sendLoginResponse(Request $request)
    {
        $request->session()->regenerate();

        if ($this->isUserAlreadyLogin()) {
            $this->revokeAll();

            return to_route('login')->withErrors(['is_already_logged_in' => true])->with(['email' => $request->email]);
        }

        $this->updateLastLogInLocation();
        $this->clearLoginAttempts($request);

        createInitSubscription($this->guard()->user());

        // Check if the user has verified their email address
        $user = $this->guard()->user();
        if ($user->verified != 1) {
            return $request->wantsJson()
                ? new JsonResponse(['message' => 'Your email address is not verified.'], 409)
                : redirect()->route('verification.notice');
        }

        if ($response = $this->authenticated($request, $user)) {
            return $response;
        }

        // Check if we have an authorization request in the session
        if ($request->session()->has('authRequest')) {
            $authRequest = $request->session()->pull('authRequest');

            // Build the authorization URL
            $query = http_build_query($authRequest);
            return redirect('/oauth/authorize?' . $query);
        }

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect()->intended($this->redirectPath());
    }

    /**
     * Attempt to log the user into the application.
     *
     * @param  \Illuminate\Http\Request  $request
     *
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        return $this->guard()->attempt($this->credentials($request), true);
    }
}
