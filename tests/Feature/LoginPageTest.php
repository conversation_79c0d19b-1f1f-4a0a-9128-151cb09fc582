<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LoginPageTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that direct access to login page works without redirect loops.
     */
    public function test_login_page_loads_without_redirect_loop()
    {
        $response = $this->get('/login');

        // Debug: Check where it's redirecting
        if ($response->status() === 302) {
            $location = $response->headers->get('Location');
            $this->fail("Login page is redirecting to: " . $location);
        }

        // Should return 200 OK, not a redirect
        $response->assertOk();
        $response->assertViewIs('auth.login');
    }

    /**
     * Test that login page with email parameter doesn't trigger registration flow.
     */
    public function test_login_page_with_email_parameter_shows_login_form()
    {
        $response = $this->get('/login?email=<EMAIL>');

        // Should show login form, not redirect to register
        $response->assertOk();
        $response->assertViewIs('auth.login');
    }

    /**
     * Test that registration flow only triggers with is_register=1 parameter.
     */
    public function test_registration_flow_only_triggers_with_is_register_parameter()
    {
        // Ensure we're not authenticated
        $this->assertGuest();

        $response = $this->get('/login?is_register=1&email=<EMAIL>');

        // Debug: Check where it's redirecting
        if ($response->status() === 302) {
            $location = $response->headers->get('Location');
            if (!str_contains($location, '/register')) {
                $this->fail("Registration flow is redirecting to: " . $location . " instead of /register");
            }
        }

        // Should redirect to register page
        $response->assertRedirect();
        $this->assertStringContainsString('/register', $response->headers->get('Location'));
    }

    /**
     * Test that register page loads correctly.
     */
    public function test_register_page_loads_correctly()
    {
        $response = $this->get('/register');

        $response->assertOk();
        $response->assertViewIs('auth.register');
    }
}
